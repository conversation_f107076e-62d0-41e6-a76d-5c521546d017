<?php $__env->startSection('css'); ?>
    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css')); ?>">
    <style>
        .student-card {
            transition: all 0.3s ease;
        }
        .student-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .filter-bar {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .stats-card {
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: scale(1.02);
        }
        .modal-xl {
            max-width: 90%;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <!-- jQuery (required for DataTables plugin) -->
    <script src="<?php echo e(asset('js/lib/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<div>
    <!-- Hero -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 fw-bold mb-2">Gestion des Étudiants</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-alt">
                            <li class="breadcrumb-item"><a href="#">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Étudiants</li>
                        </ol>
                    </nav>
                </div>
                
            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Filters & Stats Overview -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="block block-rounded filter-bar">
                    <div class="row g-3 align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">Recherche</label>
                            <input type="search" wire:model="query" class="form-control" placeholder="Nom, prénom...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Niveau</label>
                            <select wire:model="filtreNiveau" class="form-select">
                                <option value="">Tous les niveaux</option>
                                <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Année</label>
                            <select wire:model="filtreAnnee" class="form-select">
                                <option value="">Toutes les années</option>
                                <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">État</label>
                            <select wire:model="filtreRempli" class="form-select">
                                <option value="">Tous les étudiants</option>
                                <option value="non_rempli">Non remplis</option>
                                <option value="rempli">Remplis</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="block block-rounded stats-card bg-primary-light">
                    <div class="block-content">
                        <div class="row text-center">
                            <div class="col-12">
                                <div class="py-3">
                                    <div class="fs-2 fw-bold text-primary mb-0"><?php echo e($totalEtuCount); ?></div>
                                    <div class="fs-sm fw-semibold text-uppercase">Total Étudiants</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Students List -->
            <div class="col-md-8">
                <div class="block block-rounded">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">Liste des Étudiants</h3>
                        <div class="block-options">
                            <button type="button" class="btn btn-sm btn-alt-secondary" data-toggle="block-option" data-action="fullscreen_toggle">
                                <i class="fa fa-expand-alt"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-alt-secondary" data-toggle="block-option" data-action="content_toggle">
                                <i class="fa fa-fw fa-caret-down"></i>
                            </button>
                        </div>
                    </div>
                    <div class="block-content block-content-full">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                                <thead>
                                    <tr>
                                        <th>Nom et Prénom</th>
                                        <th>Niveau</th>
                                        <th>Année Universitaire</th>
                                        <th class="text-center" style="width: 120px;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $etus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $etu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="fw-semibold">
                                                <?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>

                                            </td>
                                            <td>
                                                <?php if($etu->niveau == null): ?>
                                                    <span class="badge bg-warning">Non défini</span>
                                                <?php else: ?>
                                                    <?php echo e($etu->niveau->nom); ?>

                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($etu->annee->nom); ?></td>
                                            <td class="text-center">
                                                <?php if($etu->parcours == null): ?>
                                                    <button type="button" class="btn btn-sm btn-alt-secondary" 
                                                        wire:click="goToEditUser(<?php echo e($etu->user->id); ?>)"
                                                        data-bs-toggle="modal" data-bs-target="#modal-student-edit">
                                                        <i class="fa fa-fw fa-pencil-alt"></i> Remplir
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-alt-success"
                                                        wire:click="goToEditUser(<?php echo e($etu->user->id); ?>)"
                                                        data-bs-toggle="modal" data-bs-target="#modal-student-edit">
                                                        <i class="fa fa-check"></i> Modifier
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <nav aria-label="Photos Search Navigation">
                            <ul class="pagination pagination-sm justify-content-end mt-2">
                                <?php echo e($etus->links()); ?>

                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="col-md-4">
                <div class="block block-rounded">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">Statistiques par Parcours</h3>
                    </div>
                    <div class="block-content">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-vcenter fs-sm">
                                <thead>
                                    <tr>
                                        <th>Parcours</th>
                                        <th class="text-center">Nombre</th>
                                        <th class="text-center">%</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $etuStatus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($parcour->nom); ?></td>
                                            <td class="text-center"><?php echo e($parcour->etu_count); ?></td>
                                            <td class="text-center">
                                                <?php if($totalEtuCount > 0): ?>
                                                    <?php echo e(round(($parcour->etu_count / $totalEtuCount) * 100)); ?>%
                                                <?php else: ?>
                                                    0%
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="3" class="text-center">Aucun parcours trouvé</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Page Content -->

    <!-- Student Edit Modal -->
    <div wire:ignore.self class="modal fade" id="modal-student-edit" tabindex="-1" role="dialog" aria-labelledby="modal-student-edit" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <?php if(isset($editUser['nom'])): ?>
                            Modifier étudiant: <?php echo e($editUser['nom'] ?? ''); ?> <?php echo e($editUser['prenom'] ?? ''); ?>

                        <?php else: ?>
                            Informations de l'étudiant
                        <?php endif; ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <?php if(isset($editUser['id'])): ?>
                        <form wire:submit.prevent="updateUser">
                            <div class="row">
                                <!-- Student Personal Info -->
                                <div class="col-md-6">
                                    <h4 class="mb-3">Informations personnelles</h4>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Nom <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['editUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               wire:model.defer="editUser.nom">
                                        <?php $__errorArgs = ['editUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Prénom</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.prenom">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Sexe <span class="text-danger">*</span></label>
                                            <select class="form-select <?php $__errorArgs = ['editUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                    wire:model.defer="editUser.sexe">
                                                <option value="">Choisir</option>
                                                <option value="H">Homme</option>
                                                <option value="F">Femme</option>
                                            </select>
                                            <?php $__errorArgs = ['editUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Date naissance <span class="text-danger">*</span></label>
                                            <input type="text" id="js-datepicker-naissance" class="js-datepicker form-control <?php $__errorArgs = ['editUser.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   data-name="date_naissance"
                                                   value="<?php echo e($editUser['date_naissance'] ?? ''); ?>"
                                                   data-week-start="1" data-autoclose="true" data-today-highlight="true" data-date-format="dd/mm/yyyy">
                                            <?php $__errorArgs = ['editUser.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Lieu naissance <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['editUser.lieu_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   wire:model.defer="editUser.lieu_naissance">
                                            <?php $__errorArgs = ['editUser.lieu_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" wire:model.defer="editUser.email">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Matricule</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.matricule" readonly>
                                            <small class="text-muted">Le matricule est généré automatiquement</small>
                                            <?php $__errorArgs = ['editUser.matricule'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    
                                    
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone 1 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['editUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   wire:model.defer="editUser.telephone1">
                                            <?php $__errorArgs = ['editUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone 2</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.telephone2">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Adresse</label>
                                        <textarea class="form-control" rows="2" wire:model.defer="editUser.adresse"></textarea>
                                    </div>
                                </div>
                                
                                <!-- Academic Info and Family Info -->
                                <div class="col-md-6">
                                    <h4 class="mb-3">Informations académiques</h4>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Parcours <span class="text-danger">*</span></label>
                                            <select class="form-select <?php $__errorArgs = ['editUser.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                    wire:model.defer="editUser.parcour_id">
                                                <option value="">Sélectionner</option>
                                                <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->nom); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['editUser.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Niveau <span class="text-danger">*</span></label>
                                            <select class="form-select <?php $__errorArgs = ['editUser.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                    wire:model.defer="editUser.niveau_id">
                                                <option value="">Sélectionner</option>
                                                <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['editUser.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">CIN</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.cin">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Duplicata</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.duplicata">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Date délivrance</label>
                                            <input type="text" id="js-datepicker-delivrance" class="js-datepicker date-delivrance form-control" 
                                                   data-name="date_delivrance"
                                                   value="<?php echo e($editUser['date_delivrance'] ?? ''); ?>"
                                                   data-week-start="1" data-autoclose="true" data-today-highlight="true" data-date-format="dd/mm/yy">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Lieu délivrance</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.lieu_delivrance">
                                        </div>
                                    </div>
                                    
                                    <h4 class="mt-4 mb-3">Informations familiales</h4>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Nom du père</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.nom_pere">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone du père</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.tel_pere">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Nom de la mère</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.nom_mere">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone de la mère</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.tel_mere">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Nom du tuteur</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.nom_tuteur">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone du tuteur</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.tel_tuteur">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Payment History -->
                            <?php if(isset($payments) && count($payments) > 0): ?>
                                <div class="mt-4">
                                    <h4>Historique des paiements</h4>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Montant</th>
                                                    <th>Type</th>
                                                    <th>État</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($payment->created_at->format('d/m/Y')); ?></td>
                                                        <td><?php echo e($payment->montant); ?></td>
                                                        <td><?php echo e($payment->payment->nom); ?></td>
                                                        <td>
                                                            <?php if($payment->is_valid_sec): ?>
                                                                <span class="badge bg-success">Validé</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-warning">En attente</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if(!$payment->is_valid_sec): ?>
                                                                <button type="button" class="btn btn-sm btn-success" 
                                                                        wire:click="valider(<?php echo e($payment->id); ?>)">
                                                                    <i class="fa fa-check"></i> Valider
                                                                </button>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="d-flex justify-content-end mt-4">
                                <button type="button" class="btn btn-alt-secondary me-2" data-bs-dismiss="modal">Annuler</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-fw fa-check me-1"></i> Enregistrer
                                </button>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Chargement des données...</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- New Student Modal -->
    <div wire:ignore.self class="modal fade" id="modal-new-student" tabindex="-1" role="dialog" aria-labelledby="modal-new-student" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un nouvel étudiant</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Form will be added here for new student creation -->
                    <form wire:submit.prevent="createUser">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Nom <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Prénom</label>
                                <input type="text" class="form-control">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Téléphone <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Niveau <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">Sélectionner</option>
                                    <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Année Universitaire <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">Sélectionner</option>
                                    <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" class="btn btn-alt-secondary me-2" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-fw fa-plus me-1"></i> Créer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        window.addEventListener("helperDatePicker", event => {
        One.helpersOnLoad(['jq-datepicker']);
    })
        
        // Fix datepicker issue with Livewire
        $(document).on('change', '.js-datepicker', function() {
            let input = $(this);
            let value = input.val();
            let name = input.attr('name') || input.data('name');
            
            // Si le datepicker est celui de la date de naissance
            if (input.hasClass('date-naissance') || input.attr('id') === 'js-datepicker-naissance') {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('editUser.date_naissance', value);
            }
            
            // Si le datepicker est celui de la date de délivrance
            if (input.hasClass('date-delivrance')) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('editUser.date_delivrance', value);
            }
        });
        
        // Show success notifications
        window.addEventListener("showSuccessMessage", event => {
            One.helpersOnLoad(['jq-notify']);
            One.helpers('jq-notify', {
                type: 'success',
                icon: 'fa fa-check me-1',
                message: event.detail.message || 'Opération effectuée avec succès!'
            });
        });
        
        // Close modal event listener
        window.addEventListener('closeModal', event => {
            // Close the edit student modal
            let studentEditModal = bootstrap.Modal.getInstance(document.getElementById('modal-student-edit'));
            if (studentEditModal) {
                studentEditModal.hide();
            }
            
            // Close the new student modal
            let newStudentModal = bootstrap.Modal.getInstance(document.getElementById('modal-new-student'));
            if (newStudentModal) {
                newStudentModal.hide();
            }
        });
        
        // Reinitialiser les datepickers lors de l'affichage de la modal
        document.getElementById('modal-student-edit').addEventListener('shown.bs.modal', function () {
            One.helpersOnLoad(['jq-datepicker']);
            
            // Ajouter une classe spécifique aux datepickers pour les identifier
            $('.js-datepicker').each(function() {
                let input = $(this);
                if (input.attr('wire:model.defer') && input.attr('wire:model.defer').includes('date_naissance')) {
                    input.addClass('date-naissance');
                }
                if (input.attr('wire:model.defer') && input.attr('wire:model.defer').includes('date_delivrance')) {
                    input.addClass('date-delivrance');
                }
            });
        });
        
        // Update chart when page loads
        updatePieChart();
        
        // Update chart when Livewire updates the DOM
        document.addEventListener('livewire:load', function () {
            Livewire.hook('message.processed', (message, component) => {
                updatePieChart();
            });
        });
    });
    
    // Create pie chart for parcours data
    const updatePieChart = () => {
        const chartCanvas = document.getElementById('parcoursPieChart');
        if (chartCanvas) {
            // Destroy existing chart if it exists
            if (window.parcoursChart) {
                window.parcoursChart.destroy();
            }
            
            // Get data from the table
            const rows = document.querySelectorAll('table tbody tr');
            const labels = [];
            const data = [];
            
            rows.forEach(row => {
                const columns = row.querySelectorAll('td');
                if (columns.length >= 2) {
                    const parcoursName = columns[0].textContent.trim();
                    const etuCount = parseInt(columns[1].textContent.trim(), 10);
                    
                    if (parcoursName && !isNaN(etuCount)) {
                        labels.push(parcoursName);
                        data.push(etuCount);
                    }
                }
            });
            
            if (labels.length > 0) {
                const ctx = chartCanvas.getContext('2d');
                window.parcoursChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: [
                                '#3B82F6', '#10B981', '#F59E0B', '#EF4444', 
                                '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
                                '#6366F1', '#14B8A6', '#F97316', '#DC2626'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'right',
                            }
                        }
                    }
                });
            }
        }
    };
</script><?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/secretaire/infoetu/index.blade.php ENDPATH**/ ?>