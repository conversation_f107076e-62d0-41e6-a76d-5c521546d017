@section('css')
    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="{{ asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css') }}">
    <style>
        .student-card {
            transition: all 0.3s ease;
        }
        .student-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .filter-bar {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .stats-card {
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: scale(1.02);
        }
        .modal-xl {
            max-width: 90%;
        }
    </style>
@endsection

@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    <script src="{{ asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
@endsection

<div>
    <!-- Hero -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 fw-bold mb-2">Gestion des Étudiants</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-alt">
                            <li class="breadcrumb-item"><a href="#">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Étudiants</li>
                        </ol>
                    </nav>
                </div>
                {{-- <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-new-student">
                    <i class="fa fa-fw fa-user-plus me-1"></i> Nouvel Étudiant
                </button> --}}
            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Filters & Stats Overview -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="block block-rounded filter-bar">
                    <div class="row g-3 align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">Recherche</label>
                            <input type="search" wire:model="query" class="form-control" placeholder="Nom, prénom...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Niveau</label>
                            <select wire:model="filtreNiveau" class="form-select">
                                <option value="">Tous les niveaux</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Année</label>
                            <select wire:model="filtreAnnee" class="form-select">
                                <option value="">Toutes les années</option>
                                @foreach ($annees as $annee)
                                    <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">État</label>
                            <select wire:model="filtreRempli" class="form-select">
                                <option value="">Tous les étudiants</option>
                                <option value="non_rempli">Non remplis</option>
                                <option value="rempli">Remplis</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="block block-rounded stats-card bg-primary-light">
                    <div class="block-content">
                        <div class="row text-center">
                            <div class="col-12">
                                <div class="py-3">
                                    <div class="fs-2 fw-bold text-primary mb-0">{{ $totalEtuCount }}</div>
                                    <div class="fs-sm fw-semibold text-uppercase">Total Étudiants</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Students List -->
            <div class="col-md-8">
                <div class="block block-rounded">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">Liste des Étudiants</h3>
                        <div class="block-options">
                            <button type="button" class="btn btn-sm btn-alt-secondary" data-toggle="block-option" data-action="fullscreen_toggle">
                                <i class="fa fa-expand-alt"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-alt-secondary" data-toggle="block-option" data-action="content_toggle">
                                <i class="fa fa-fw fa-caret-down"></i>
                            </button>
                        </div>
                    </div>
                    <div class="block-content block-content-full">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                                <thead>
                                    <tr>
                                        <th>Nom et Prénom</th>
                                        <th>Niveau</th>
                                        <th>Année Universitaire</th>
                                        <th class="text-center" style="width: 120px;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($etus as $etu)
                                        <tr>
                                            <td class="fw-semibold">
                                                {{ $etu->user->nom }} {{ $etu->user->prenom }}
                                            </td>
                                            <td>
                                                @if ($etu->niveau == null)
                                                    <span class="badge bg-warning">Non défini</span>
                                                @else
                                                    {{ $etu->niveau->nom }}
                                                @endif
                                            </td>
                                            <td>{{ $etu->annee->nom }}</td>
                                            <td class="text-center">
                                                @if ($etu->parcours == null)
                                                    <button type="button" class="btn btn-sm btn-alt-secondary" 
                                                        wire:click="goToEditUser({{ $etu->user->id }})"
                                                        data-bs-toggle="modal" data-bs-target="#modal-student-edit">
                                                        <i class="fa fa-fw fa-pencil-alt"></i> Remplir
                                                    </button>
                                                @else
                                                    <button type="button" class="btn btn-sm btn-alt-success"
                                                        wire:click="goToEditUser({{ $etu->user->id }})"
                                                        data-bs-toggle="modal" data-bs-target="#modal-student-edit">
                                                        <i class="fa fa-check"></i> Modifier
                                                    </button>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <nav aria-label="Photos Search Navigation">
                            <ul class="pagination pagination-sm justify-content-end mt-2">
                                {{ $etus->links() }}
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="col-md-4">
                <div class="block block-rounded">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">Statistiques par Parcours</h3>
                    </div>
                    <div class="block-content">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-vcenter fs-sm">
                                <thead>
                                    <tr>
                                        <th>Parcours</th>
                                        <th class="text-center">Nombre</th>
                                        <th class="text-center">%</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($etuStatus as $parcour)
                                        <tr>
                                            <td>{{ $parcour->nom }}</td>
                                            <td class="text-center">{{ $parcour->etu_count }}</td>
                                            <td class="text-center">
                                                @if($totalEtuCount > 0)
                                                    {{ round(($parcour->etu_count / $totalEtuCount) * 100) }}%
                                                @else
                                                    0%
                                                @endif
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="3" class="text-center">Aucun parcours trouvé</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                        
                        {{-- <div class="mt-4">
                            <canvas id="parcoursPieChart" height="250"></canvas>
                        </div> --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Page Content -->

    <!-- Student Edit Modal -->
    <div wire:ignore.self class="modal fade" id="modal-student-edit" tabindex="-1" role="dialog" aria-labelledby="modal-student-edit" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @if(isset($editUser['nom']))
                            Modifier étudiant: {{ $editUser['nom'] ?? '' }} {{ $editUser['prenom'] ?? '' }}
                        @else
                            Informations de l'étudiant
                        @endif
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    @if(isset($editUser['id']))
                        <form wire:submit.prevent="updateUser">
                            <div class="row">
                                <!-- Student Personal Info -->
                                <div class="col-md-6">
                                    <h4 class="mb-3">Informations personnelles</h4>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Nom <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('editUser.nom') is-invalid @enderror" 
                                               wire:model.defer="editUser.nom">
                                        @error('editUser.nom')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Prénom</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.prenom">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Sexe <span class="text-danger">*</span></label>
                                            <select class="form-select @error('editUser.sexe') is-invalid @enderror" 
                                                    wire:model.defer="editUser.sexe">
                                                <option value="">Choisir</option>
                                                <option value="H">Homme</option>
                                                <option value="F">Femme</option>
                                            </select>
                                            @error('editUser.sexe')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Date naissance <span class="text-danger">*</span></label>
                                            <input type="text" id="js-datepicker-naissance" class="js-datepicker form-control @error('editUser.date_naissance') is-invalid @enderror"
                                                   data-name="date_naissance"
                                                   value="{{ $editUser['date_naissance'] ?? '' }}"
                                                   data-week-start="1" data-autoclose="true" data-today-highlight="true" data-date-format="dd/mm/yyyy">
                                            @error('editUser.date_naissance')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Lieu naissance <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('editUser.lieu_naissance') is-invalid @enderror" 
                                                   wire:model.defer="editUser.lieu_naissance">
                                            @error('editUser.lieu_naissance')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" wire:model.defer="editUser.email">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Matricule</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.matricule" readonly>
                                            <small class="text-muted">Le matricule est généré automatiquement</small>
                                            @error('editUser.matricule')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    {{-- <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label class="form-label">Matricule</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.matricule" readonly>
                                            <small class="text-muted">Le matricule est généré automatiquement</small>
                                        </div>
                                    </div> --}}
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone 1 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('editUser.telephone1') is-invalid @enderror" 
                                                   wire:model.defer="editUser.telephone1">
                                            @error('editUser.telephone1')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone 2</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.telephone2">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Adresse</label>
                                        <textarea class="form-control" rows="2" wire:model.defer="editUser.adresse"></textarea>
                                    </div>
                                </div>
                                
                                <!-- Academic Info and Family Info -->
                                <div class="col-md-6">
                                    <h4 class="mb-3">Informations académiques</h4>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Parcours <span class="text-danger">*</span></label>
                                            <select class="form-select @error('editUser.parcour_id') is-invalid @enderror" 
                                                    wire:model.defer="editUser.parcour_id">
                                                <option value="">Sélectionner</option>
                                                @foreach ($parcours as $parcour)
                                                    <option value="{{ $parcour->id }}">{{ $parcour->nom }}</option>
                                                @endforeach
                                            </select>
                                            @error('editUser.parcour_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Niveau <span class="text-danger">*</span></label>
                                            <select class="form-select @error('editUser.niveau_id') is-invalid @enderror" 
                                                    wire:model.defer="editUser.niveau_id">
                                                <option value="">Sélectionner</option>
                                                @foreach ($niveaux as $niveau)
                                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                                @endforeach
                                            </select>
                                            @error('editUser.niveau_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">CIN</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.cin">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Duplicata</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.duplicata">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Date délivrance</label>
                                            <input type="text" id="js-datepicker-delivrance" class="js-datepicker date-delivrance form-control" 
                                                   data-name="date_delivrance"
                                                   value="{{ $editUser['date_delivrance'] ?? '' }}"
                                                   data-week-start="1" data-autoclose="true" data-today-highlight="true" data-date-format="dd/mm/yy">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Lieu délivrance</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.lieu_delivrance">
                                        </div>
                                    </div>
                                    
                                    <h4 class="mt-4 mb-3">Informations familiales</h4>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Nom du père</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.nom_pere">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone du père</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.tel_pere">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Nom de la mère</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.nom_mere">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone de la mère</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.tel_mere">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Nom du tuteur</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.nom_tuteur">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Téléphone du tuteur</label>
                                            <input type="text" class="form-control" wire:model.defer="editUser.tel_tuteur">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Payment History -->
                            @if(isset($payments) && count($payments) > 0)
                                <div class="mt-4">
                                    <h4>Historique des paiements</h4>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Montant</th>
                                                    <th>Type</th>
                                                    <th>État</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($payments as $payment)
                                                    <tr>
                                                        <td>{{ $payment->created_at->format('d/m/Y') }}</td>
                                                        <td>{{ $payment->montant }}</td>
                                                        <td>{{ $payment->payment->nom }}</td>
                                                        <td>
                                                            @if($payment->is_valid_sec)
                                                                <span class="badge bg-success">Validé</span>
                                                            @else
                                                                <span class="badge bg-warning">En attente</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @if(!$payment->is_valid_sec)
                                                                <button type="button" class="btn btn-sm btn-success" 
                                                                        wire:click="valider({{ $payment->id }})">
                                                                    <i class="fa fa-check"></i> Valider
                                                                </button>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            @endif
                            
                            <div class="d-flex justify-content-end mt-4">
                                <button type="button" class="btn btn-alt-secondary me-2" data-bs-dismiss="modal">Annuler</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-fw fa-check me-1"></i> Enregistrer
                                </button>
                            </div>
                        </form>
                    @else
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Chargement des données...</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- New Student Modal -->
    <div wire:ignore.self class="modal fade" id="modal-new-student" tabindex="-1" role="dialog" aria-labelledby="modal-new-student" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un nouvel étudiant</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Form will be added here for new student creation -->
                    <form wire:submit.prevent="createUser">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Nom <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Prénom</label>
                                <input type="text" class="form-control">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Téléphone <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Niveau <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">Sélectionner</option>
                                    @foreach ($niveaux as $niveau)
                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Année Universitaire <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">Sélectionner</option>
                                    @foreach ($annees as $annee)
                                        <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" class="btn btn-alt-secondary me-2" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-fw fa-plus me-1"></i> Créer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        window.addEventListener("helperDatePicker", event => {
        One.helpersOnLoad(['jq-datepicker']);
    })
        
        // Fix datepicker issue with Livewire
        $(document).on('change', '.js-datepicker', function() {
            let input = $(this);
            let value = input.val();
            let name = input.attr('name') || input.data('name');
            
            // Si le datepicker est celui de la date de naissance
            if (input.hasClass('date-naissance') || input.attr('id') === 'js-datepicker-naissance') {
                @this.set('editUser.date_naissance', value);
            }
            
            // Si le datepicker est celui de la date de délivrance
            if (input.hasClass('date-delivrance')) {
                @this.set('editUser.date_delivrance', value);
            }
        });
        
        // Show success notifications
        window.addEventListener("showSuccessMessage", event => {
            One.helpersOnLoad(['jq-notify']);
            One.helpers('jq-notify', {
                type: 'success',
                icon: 'fa fa-check me-1',
                message: event.detail.message || 'Opération effectuée avec succès!'
            });
        });
        
        // Close modal event listener
        window.addEventListener('closeModal', event => {
            // Close the edit student modal
            let studentEditModal = bootstrap.Modal.getInstance(document.getElementById('modal-student-edit'));
            if (studentEditModal) {
                studentEditModal.hide();
            }
            
            // Close the new student modal
            let newStudentModal = bootstrap.Modal.getInstance(document.getElementById('modal-new-student'));
            if (newStudentModal) {
                newStudentModal.hide();
            }
        });
        
        // Reinitialiser les datepickers lors de l'affichage de la modal
        document.getElementById('modal-student-edit').addEventListener('shown.bs.modal', function () {
            One.helpersOnLoad(['jq-datepicker']);
            
            // Ajouter une classe spécifique aux datepickers pour les identifier
            $('.js-datepicker').each(function() {
                let input = $(this);
                if (input.attr('wire:model.defer') && input.attr('wire:model.defer').includes('date_naissance')) {
                    input.addClass('date-naissance');
                }
                if (input.attr('wire:model.defer') && input.attr('wire:model.defer').includes('date_delivrance')) {
                    input.addClass('date-delivrance');
                }
            });
        });
        
        // Update chart when page loads
        updatePieChart();
        
        // Update chart when Livewire updates the DOM
        document.addEventListener('livewire:load', function () {
            Livewire.hook('message.processed', (message, component) => {
                updatePieChart();
            });
        });
    });
    
    // Create pie chart for parcours data
    const updatePieChart = () => {
        const chartCanvas = document.getElementById('parcoursPieChart');
        if (chartCanvas) {
            // Destroy existing chart if it exists
            if (window.parcoursChart) {
                window.parcoursChart.destroy();
            }
            
            // Get data from the table
            const rows = document.querySelectorAll('table tbody tr');
            const labels = [];
            const data = [];
            
            rows.forEach(row => {
                const columns = row.querySelectorAll('td');
                if (columns.length >= 2) {
                    const parcoursName = columns[0].textContent.trim();
                    const etuCount = parseInt(columns[1].textContent.trim(), 10);
                    
                    if (parcoursName && !isNaN(etuCount)) {
                        labels.push(parcoursName);
                        data.push(etuCount);
                    }
                }
            });
            
            if (labels.length > 0) {
                const ctx = chartCanvas.getContext('2d');
                window.parcoursChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: [
                                '#3B82F6', '#10B981', '#F59E0B', '#EF4444', 
                                '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
                                '#6366F1', '#14B8A6', '#F97316', '#DC2626'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'right',
                            }
                        }
                    }
                });
            }
        }
    };
</script>