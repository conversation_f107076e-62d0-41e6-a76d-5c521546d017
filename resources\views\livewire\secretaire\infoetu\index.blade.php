@section('css')
    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="{{ asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css') }}">
    <style>
        /* IMSAA Color Scheme */
        :root {
            --primary-blue: #3498db;
            --darker-blue: #2980b9;
            --red-accent: #e74c3c;
            --professional-gray: #2c3e50;
            --light-gray: #34495e;
            --success-green: #27ae60;
            --warning-orange: #f39c12;
        }

        /* Modern Card Styles */
        .student-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .student-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced Filter Bar */
        .filter-bar {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Statistics Cards */
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--darker-blue));
        }

        /* Enhanced Table Styles */
        .table-modern {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .table-modern thead th {
            background: linear-gradient(135deg, var(--professional-gray), var(--light-gray));
            color: white;
            font-weight: 600;
            border: none;
            padding: 16px 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .table-modern thead th:hover {
            background: var(--darker-blue);
        }
        .table-modern tbody tr {
            transition: all 0.2s ease;
        }
        .table-modern tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.05);
            transform: scale(1.01);
        }

        /* Loading States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        /* Action Buttons */
        .btn-action {
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
        }
        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        .status-rempli {
            background: linear-gradient(135deg, var(--success-green), #2ecc71);
            color: white;
        }
        .status-non-rempli {
            background: linear-gradient(135deg, var(--warning-orange), #e67e22);
            color: white;
        }

        /* Modal Enhancements */
        .modal-xl {
            max-width: 95%;
        }
        .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-blue), var(--darker-blue));
            color: white;
            border-radius: 16px 16px 0 0;
            padding: 20px 24px;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .modal-xl {
                max-width: 98%;
                margin: 1rem auto;
            }
        }

        @media (max-width: 992px) {
            .stats-card .card-body {
                padding: 1rem;
            }
            .table-modern thead th {
                padding: 12px 8px;
                font-size: 0.9rem;
            }
            .btn-action {
                padding: 6px 12px;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 768px) {
            .filter-bar {
                padding: 16px;
            }
            .stats-card {
                margin-bottom: 16px;
            }
            .table-responsive {
                border-radius: 8px;
            }

            /* Mobile-first table design */
            .table-modern {
                font-size: 0.85rem;
            }
            .table-modern thead th {
                padding: 8px 6px;
            }
            .table-modern tbody td {
                padding: 8px 6px;
            }

            /* Stack action buttons vertically on mobile */
            .btn-group {
                flex-direction: column;
                gap: 4px;
            }
            .btn-group .btn {
                width: 100%;
                margin: 0;
            }

            /* Improve modal on mobile */
            .modal-dialog {
                margin: 0.5rem;
            }
            .modal-content {
                border-radius: 12px;
            }

            /* Hide less important columns on mobile */
            .d-none-mobile {
                display: none !important;
            }
        }

        @media (max-width: 576px) {
            .hero-section h1 {
                font-size: 1.5rem;
            }
            .breadcrumb {
                font-size: 0.85rem;
            }

            /* Compact stats cards on small screens */
            .stats-card .fs-3 {
                font-size: 1.5rem !important;
            }
            .stats-card .card-body {
                padding: 0.75rem;
            }

            /* Single column layout for filters */
            .filter-bar .row > div {
                margin-bottom: 1rem;
            }

            /* Simplified table for very small screens */
            .table-mobile-simple {
                display: block;
            }
            .table-mobile-simple thead {
                display: none;
            }
            .table-mobile-simple tbody,
            .table-mobile-simple tr,
            .table-mobile-simple td {
                display: block;
                width: 100%;
            }
            .table-mobile-simple tr {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-bottom: 1rem;
                padding: 1rem;
                background: white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .table-mobile-simple td {
                border: none;
                padding: 0.25rem 0;
                position: relative;
                padding-left: 50%;
            }
            .table-mobile-simple td:before {
                content: attr(data-label) ": ";
                position: absolute;
                left: 0;
                width: 45%;
                font-weight: bold;
                color: var(--professional-gray);
            }
        }

        /* Accessibility Improvements */
        .btn:focus,
        .form-control:focus,
        .form-select:focus {
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            border-color: var(--primary-blue);
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .stats-card {
                border: 2px solid #000;
            }
            .table-modern thead th {
                border: 1px solid #000;
            }
            .btn-action {
                border: 2px solid currentColor;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .student-card,
            .stats-card,
            .btn-action,
            .table-modern tbody tr {
                transition: none;
            }
            .fade-in,
            .slide-in {
                animation: none;
            }
        }

        /* Dark mode support (if needed in future) */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #1a1a1a;
                --text-color: #ffffff;
                --card-bg: #2d2d2d;
            }
        }

        /* Print styles */
        @media print {
            .btn,
            .modal,
            .filter-bar,
            .pagination {
                display: none !important;
            }
            .table-modern {
                border-collapse: collapse;
            }
            .table-modern th,
            .table-modern td {
                border: 1px solid #000 !important;
                padding: 8px !important;
            }
            .stats-card {
                break-inside: avoid;
                margin-bottom: 1rem;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
@endsection

@section('js')
    <!-- jQuery (required for DataTables plugin) -->
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    <script src="{{ asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
@endsection

<div>
    <!-- Enhanced Hero Section -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center py-3">
                <div class="fade-in">
                    <h1 class="h2 fw-bold mb-2 text-primary">
                        <i class="fa fa-users me-2"></i>
                        Gestion des Informations Étudiants
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-alt">
                            <li class="breadcrumb-item">
                                <a href="#" class="text-decoration-none">
                                    <i class="fa fa-home me-1"></i>Dashboard
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <i class="fa fa-user-graduate me-1"></i>Informations Étudiants
                            </li>
                        </ol>
                    </nav>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex gap-2 mt-3 mt-md-0 slide-in">
                    <button type="button" class="btn btn-outline-primary btn-action"
                            wire:click="toggleFilters"
                            title="Basculer les filtres">
                        <i class="fa fa-filter me-1"></i>
                        <span class="d-none d-sm-inline">Filtres</span>
                    </button>

                    <button type="button" class="btn btn-outline-secondary btn-action"
                            wire:click="toggleCompactView"
                            title="Vue compacte">
                        <i class="fa fa-{{ $compactView ? 'expand' : 'compress' }} me-1"></i>
                        <span class="d-none d-sm-inline">{{ $compactView ? 'Étendue' : 'Compacte' }}</span>
                    </button>

                    <div class="dropdown">
                        <button class="btn btn-primary btn-action dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-cog me-1"></i>
                            <span class="d-none d-sm-inline">Actions</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="#" wire:click="clearFilters">
                                    <i class="fa fa-eraser me-2"></i>Effacer les filtres
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="window.print()">
                                    <i class="fa fa-print me-2"></i>Imprimer
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Enhanced Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Enhanced Statistics Dashboard -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="block block-rounded stats-card bg-primary text-white fade-in">
                    <div class="block-content p-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fa fa-users fa-2x opacity-75"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fs-3 fw-bold">{{ $totalEtudiants }}</div>
                                <div class="fs-sm fw-semibold text-uppercase opacity-75">Total Étudiants</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="block block-rounded stats-card bg-success text-white fade-in" style="animation-delay: 0.1s">
                    <div class="block-content p-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fa fa-check-circle fa-2x opacity-75"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fs-3 fw-bold">{{ $totalRemplis }}</div>
                                <div class="fs-sm fw-semibold text-uppercase opacity-75">Dossiers Complets</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="block block-rounded stats-card bg-warning text-white fade-in" style="animation-delay: 0.2s">
                    <div class="block-content p-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fa fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fs-3 fw-bold">{{ $totalNonRemplis }}</div>
                                <div class="fs-sm fw-semibold text-uppercase opacity-75">À Compléter</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="block block-rounded stats-card bg-info text-white fade-in" style="animation-delay: 0.3s">
                    <div class="block-content p-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fa fa-percentage fa-2x opacity-75"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fs-3 fw-bold">
                                    {{ $totalEtudiants > 0 ? round(($totalRemplis / $totalEtudiants) * 100) : 0 }}%
                                </div>
                                <div class="fs-sm fw-semibold text-uppercase opacity-75">Taux Complétion</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Filters Section -->
        @if($showFilters)
        <div class="row mb-4">
            <div class="col-12">
                <div class="block block-rounded filter-bar slide-in">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fa fa-filter me-2 text-primary"></i>
                            Filtres de Recherche
                        </h5>
                        <button type="button" class="btn btn-sm btn-outline-secondary"
                                wire:click="clearFilters"
                                title="Effacer tous les filtres">
                            <i class="fa fa-eraser me-1"></i>Effacer
                        </button>
                    </div>

                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-search me-1 text-muted"></i>Recherche
                            </label>
                            <div class="input-group">
                                <input type="search"
                                       wire:model.debounce.300ms="query"
                                       class="form-control"
                                       placeholder="Nom, prénom, matricule...">
                                <span class="input-group-text">
                                    <i class="fa fa-search"></i>
                                </span>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-layer-group me-1 text-muted"></i>Niveau
                            </label>
                            <select wire:model="filtreNiveau" class="form-select">
                                <option value="">Tous les niveaux</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-calendar me-1 text-muted"></i>Année Universitaire
                            </label>
                            <select wire:model="filtreAnnee" class="form-select">
                                <option value="">Toutes les années</option>
                                @foreach ($annees as $annee)
                                    <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-clipboard-check me-1 text-muted"></i>État du Dossier
                            </label>
                            <select wire:model="filtreRempli" class="form-select">
                                <option value="">Tous les étudiants</option>
                                <option value="non_rempli">Dossiers incomplets</option>
                                <option value="rempli">Dossiers complets</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <div class="row">
            <!-- Enhanced Students List -->
            <div class="col-lg-8">
                <div class="block block-rounded student-card">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">
                            <i class="fa fa-list me-2 text-primary"></i>
                            Liste des Étudiants
                            <span class="badge bg-primary ms-2">{{ $etus->total() }}</span>
                        </h3>
                        <div class="block-options">
                            <div class="dropdown me-2">
                                <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-sort me-1"></i>Trier
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#" wire:click="sortBy('created_at')">
                                            <i class="fa fa-calendar me-2"></i>Date d'inscription
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" wire:click="sortBy('nom')">
                                            <i class="fa fa-sort-alpha-down me-2"></i>Nom (A-Z)
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" wire:click="sortBy('niveau_id')">
                                            <i class="fa fa-layer-group me-2"></i>Niveau
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                    data-toggle="block-option" data-action="fullscreen_toggle"
                                    title="Plein écran">
                                <i class="fa fa-expand-alt"></i>
                            </button>
                        </div>
                    </div>

                    <div class="block-content block-content-full position-relative">
                        <!-- Loading Overlay -->
                        @if($isLoading)
                        <div class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        @endif

                        <div class="table-responsive">
                            <table class="table table-modern table-hover {{ $compactView ? 'table-sm' : '' }}">
                                <thead>
                                    <tr>
                                        <th wire:click="sortBy('nom')" style="cursor: pointer;" title="Trier par nom">
                                            <i class="fa fa-user me-1"></i>Nom et Prénom
                                            @if($sortField === 'nom')
                                                <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                            @endif
                                        </th>
                                        <th>
                                            <i class="fa fa-layer-group me-1"></i>Niveau
                                        </th>
                                        <th>
                                            <i class="fa fa-calendar me-1"></i>Année
                                        </th>
                                        <th>
                                            <i class="fa fa-clipboard-check me-1"></i>État
                                        </th>
                                        <th class="text-center" style="width: 140px;">
                                            <i class="fa fa-cogs me-1"></i>Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($etus as $etu)
                                        <tr class="fade-in">
                                            <td class="fw-semibold">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-2">
                                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                             style="width: 32px; height: 32px; font-size: 0.8rem;">
                                                            {{ strtoupper(substr($etu->user->nom, 0, 1)) }}{{ strtoupper(substr($etu->user->prenom ?? '', 0, 1)) }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ $etu->user->nom }} {{ $etu->user->prenom }}</div>
                                                        @if($etu->user->matricule)
                                                            <small class="text-muted">{{ $etu->user->matricule }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if ($etu->niveau == null)
                                                    <span class="badge bg-warning">
                                                        <i class="fa fa-exclamation-triangle me-1"></i>Non défini
                                                    </span>
                                                @else
                                                    <span class="badge bg-info">{{ $etu->niveau->nom }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ $etu->annee->nom }}</span>
                                            </td>
                                            <td>
                                                @if ($etu->parcours == null)
                                                    <span class="status-badge status-non-rempli">
                                                        <i class="fa fa-clock me-1"></i>À compléter
                                                    </span>
                                                @else
                                                    <span class="status-badge status-rempli">
                                                        <i class="fa fa-check me-1"></i>Complet
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    @if ($etu->parcours == null)
                                                        <button type="button" class="btn btn-sm btn-warning btn-action"
                                                                wire:click="openEditModal({{ $etu->user->id }})"
                                                                title="Compléter le dossier">
                                                            <i class="fa fa-edit"></i>
                                                            <span class="d-none d-lg-inline ms-1">Remplir</span>
                                                        </button>
                                                    @else
                                                        <button type="button" class="btn btn-sm btn-success btn-action"
                                                                wire:click="openEditModal({{ $etu->user->id }})"
                                                                title="Modifier les informations">
                                                            <i class="fa fa-edit"></i>
                                                            <span class="d-none d-lg-inline ms-1">Modifier</span>
                                                        </button>
                                                    @endif

                                                    <button type="button" class="btn btn-sm btn-outline-info btn-action"
                                                            wire:click="openPaymentModal({{ $etu->user->id }})"
                                                            title="Voir les paiements">
                                                        <i class="fa fa-credit-card"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="fa fa-search fa-2x mb-2"></i>
                                                    <p class="mb-0">Aucun étudiant trouvé avec les critères actuels</p>
                                                    <button type="button" class="btn btn-sm btn-outline-primary mt-2"
                                                            wire:click="clearFilters">
                                                        Effacer les filtres
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Enhanced Pagination -->
                        @if($etus->hasPages())
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="text-muted">
                                Affichage de {{ $etus->firstItem() }} à {{ $etus->lastItem() }}
                                sur {{ $etus->total() }} résultats
                            </div>
                            <nav aria-label="Navigation des étudiants">
                                {{ $etus->links() }}
                            </nav>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Enhanced Statistics Sidebar -->
            <div class="col-lg-4">
                <div class="block block-rounded stats-card">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">
                            <i class="fa fa-chart-pie me-2 text-primary"></i>
                            Répartition par Parcours
                        </h3>
                        <div class="block-options">
                            <button type="button" class="btn btn-sm btn-outline-primary"
                                    onclick="refreshStats()"
                                    title="Actualiser les statistiques">
                                <i class="fa fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="block-content">
                        @if($etuStatus->count() > 0)
                            <!-- Progress Bars for Each Parcours -->
                            @foreach($etuStatus as $index => $parcour)
                                @if($parcour->etu_count > 0)
                                <div class="mb-3 fade-in" style="animation-delay: {{ $index * 0.1 }}s">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-semibold text-truncate" style="max-width: 60%;">
                                            {{ $parcour->nom }}
                                        </span>
                                        <div class="text-end">
                                            <span class="badge bg-primary">{{ $parcour->etu_count }}</span>
                                            <small class="text-muted ms-1">
                                                ({{ $totalEtudiants > 0 ? round(($parcour->etu_count / $totalEtudiants) * 100) : 0 }}%)
                                            </small>
                                        </div>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-gradient"
                                             role="progressbar"
                                             style="width: {{ $totalEtudiants > 0 ? ($parcour->etu_count / $totalEtudiants) * 100 : 0 }}%;
                                                    background: linear-gradient(90deg,
                                                        hsl({{ ($index * 60) % 360 }}, 70%, 50%),
                                                        hsl({{ (($index * 60) + 30) % 360 }}, 70%, 60%));"
                                             aria-valuenow="{{ $parcour->etu_count }}"
                                             aria-valuemin="0"
                                             aria-valuemax="{{ $totalEtudiants }}">
                                        </div>
                                    </div>
                                </div>
                                @endif
                            @endforeach

                            <!-- Summary Card -->
                            <div class="mt-4 p-3 bg-light rounded">
                                <h6 class="mb-2">
                                    <i class="fa fa-info-circle me-1 text-info"></i>
                                    Résumé
                                </h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="fs-5 fw-bold text-primary">{{ $etuStatus->where('etu_count', '>', 0)->count() }}</div>
                                        <small class="text-muted">Parcours actifs</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="fs-5 fw-bold text-success">{{ $totalRemplis }}</div>
                                        <small class="text-muted">Complets</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="fs-5 fw-bold text-warning">{{ $totalNonRemplis }}</div>
                                        <small class="text-muted">En attente</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="mt-3">
                                <h6 class="mb-2">
                                    <i class="fa fa-bolt me-1 text-warning"></i>
                                    Actions Rapides
                                </h6>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                            wire:click="$set('filtreRempli', 'non_rempli')">
                                        <i class="fa fa-exclamation-triangle me-1"></i>
                                        Voir les dossiers incomplets
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success"
                                            wire:click="$set('filtreRempli', 'rempli')">
                                        <i class="fa fa-check-circle me-1"></i>
                                        Voir les dossiers complets
                                    </button>
                                </div>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fa fa-chart-pie fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Aucune donnée disponible</p>
                                <button type="button" class="btn btn-sm btn-outline-primary"
                                        wire:click="loadStatistics">
                                    <i class="fa fa-refresh me-1"></i>Actualiser
                                </button>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Page Content -->

    <!-- Modern Student Edit Modal -->
    @if($showEditModal)
    <div class="modal fade show d-block" tabindex="-1" role="dialog" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title d-flex align-items-center">
                        <i class="fa fa-user-edit me-2"></i>
                        @if(isset($editUser['nom']))
                            Modifier étudiant: <strong class="ms-1">{{ $editUser['nom'] ?? '' }} {{ $editUser['prenom'] ?? '' }}</strong>
                        @else
                            Informations de l'étudiant
                        @endif
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeAllModals" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    @if(isset($editUser['id']))
                        <!-- Student Info Header -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="bg-light p-3 rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 48px; height: 48px; font-size: 1.2rem;">
                                                {{ strtoupper(substr($editUser['nom'] ?? '', 0, 1)) }}{{ strtoupper(substr($editUser['prenom'] ?? '', 0, 1)) }}
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-1">{{ $editUser['nom'] ?? '' }} {{ $editUser['prenom'] ?? '' }}</h5>
                                            <div class="text-muted">
                                                @if($editUser['matricule'] ?? false)
                                                    <i class="fa fa-id-card me-1"></i>{{ $editUser['matricule'] }}
                                                @else
                                                    <i class="fa fa-exclamation-triangle me-1 text-warning"></i>Matricule à générer
                                                @endif
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-info">ID: {{ $editUser['id'] ?? '' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form wire:submit.prevent="updateUser">
                            <!-- Loading Overlay -->
                            @if($isLoading)
                            <div class="loading-overlay">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Mise à jour en cours...</span>
                                </div>
                            </div>
                            @endif

                            <div class="row">
                                <!-- Student Personal Info -->
                                <div class="col-md-6">
                                    <div class="card border-0 shadow-sm mb-4">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-user me-2"></i>Informations personnelles
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label fw-semibold">
                                                    <i class="fa fa-user me-1 text-muted"></i>Nom <span class="text-danger">*</span>
                                                </label>
                                                <input type="text" class="form-control @error('editUser.nom') is-invalid @enderror"
                                                       wire:model.defer="editUser.nom"
                                                       placeholder="Entrez le nom de famille">
                                                @error('editUser.nom')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                    
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-user me-1 text-muted"></i>Prénom
                                                    </label>
                                                    <input type="text" class="form-control @error('editUser.prenom') is-invalid @enderror"
                                                           wire:model.defer="editUser.prenom"
                                                           placeholder="Entrez le prénom">
                                                    @error('editUser.prenom')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-venus-mars me-1 text-muted"></i>Sexe <span class="text-danger">*</span>
                                                    </label>
                                                    <select class="form-select @error('editUser.sexe') is-invalid @enderror"
                                                            wire:model.defer="editUser.sexe">
                                                        <option value="">Sélectionner le sexe</option>
                                                        <option value="H">👨 Homme</option>
                                                        <option value="F">👩 Femme</option>
                                                    </select>
                                                    @error('editUser.sexe')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                    
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-calendar me-1 text-muted"></i>Date de naissance <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="date"
                                                           class="form-control @error('editUser.date_naissance') is-invalid @enderror"
                                                           wire:model.defer="editUser.date_naissance"
                                                           max="{{ date('Y-m-d', strtotime('-16 years')) }}">
                                                    @error('editUser.date_naissance')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-map-marker-alt me-1 text-muted"></i>Lieu de naissance <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="text" class="form-control @error('editUser.lieu_naissance') is-invalid @enderror"
                                                           wire:model.defer="editUser.lieu_naissance"
                                                           placeholder="Ville de naissance">
                                                    @error('editUser.lieu_naissance')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                    
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-envelope me-1 text-muted"></i>Email
                                                    </label>
                                                    <input type="email" class="form-control @error('editUser.email') is-invalid @enderror"
                                                           wire:model.defer="editUser.email"
                                                           placeholder="<EMAIL>">
                                                    @error('editUser.email')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-id-card me-1 text-muted"></i>Matricule
                                                    </label>
                                                    <input type="text" class="form-control bg-light"
                                                           wire:model.defer="editUser.matricule"
                                                           readonly>
                                                    <small class="text-muted">
                                                        <i class="fa fa-info-circle me-1"></i>Généré automatiquement lors de la sauvegarde
                                                    </small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone principal <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="tel" class="form-control @error('editUser.telephone1') is-invalid @enderror"
                                                           wire:model.defer="editUser.telephone1"
                                                           placeholder="+261 XX XX XXX XX">
                                                    @error('editUser.telephone1')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone secondaire
                                                    </label>
                                                    <input type="tel" class="form-control @error('editUser.telephone2') is-invalid @enderror"
                                                           wire:model.defer="editUser.telephone2"
                                                           placeholder="+261 XX XX XXX XX">
                                                    @error('editUser.telephone2')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label fw-semibold">
                                                    <i class="fa fa-home me-1 text-muted"></i>Adresse
                                                </label>
                                                <textarea class="form-control @error('editUser.adresse') is-invalid @enderror"
                                                          rows="3"
                                                          wire:model.defer="editUser.adresse"
                                                          placeholder="Adresse complète de résidence"></textarea>
                                                @error('editUser.adresse')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Academic Info and Family Info -->
                                <div class="col-md-6">
                                    <!-- Academic Information Card -->
                                    <div class="card border-0 shadow-sm mb-4">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-graduation-cap me-2"></i>Informations académiques
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-route me-1 text-muted"></i>Parcours <span class="text-danger">*</span>
                                                    </label>
                                                    <select class="form-select @error('editUser.parcour_id') is-invalid @enderror"
                                                            wire:model.defer="editUser.parcour_id">
                                                        <option value="">Choisir un parcours</option>
                                                        @foreach ($parcours as $parcour)
                                                            <option value="{{ $parcour->id }}">
                                                                {{ $parcour->sigle }} - {{ $parcour->nom }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('editUser.parcour_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-layer-group me-1 text-muted"></i>Niveau <span class="text-danger">*</span>
                                                    </label>
                                                    <select class="form-select @error('editUser.niveau_id') is-invalid @enderror"
                                                            wire:model.defer="editUser.niveau_id">
                                                        <option value="">Choisir un niveau</option>
                                                        @foreach ($niveaux as $niveau)
                                                            <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                                        @endforeach
                                                    </select>
                                                    @error('editUser.niveau_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-id-card-alt me-1 text-muted"></i>CIN
                                                    </label>
                                                    <input type="text" class="form-control @error('editUser.cin') is-invalid @enderror"
                                                           wire:model.defer="editUser.cin"
                                                           placeholder="Numéro CIN">
                                                    @error('editUser.cin')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-copy me-1 text-muted"></i>Duplicata
                                                    </label>
                                                    <input type="text" class="form-control @error('editUser.duplicata') is-invalid @enderror"
                                                           wire:model.defer="editUser.duplicata"
                                                           placeholder="Numéro duplicata">
                                                    @error('editUser.duplicata')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-calendar-check me-1 text-muted"></i>Date de délivrance
                                                    </label>
                                                    <input type="date" class="form-control @error('editUser.date_delivrance') is-invalid @enderror"
                                                           wire:model.defer="editUser.date_delivrance">
                                                    @error('editUser.date_delivrance')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-map-marker-alt me-1 text-muted"></i>Lieu de délivrance
                                                    </label>
                                                    <input type="text" class="form-control @error('editUser.lieu_delivrance') is-invalid @enderror"
                                                           wire:model.defer="editUser.lieu_delivrance"
                                                           placeholder="Lieu de délivrance du CIN">
                                                    @error('editUser.lieu_delivrance')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Family Information Card -->
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-info text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-users me-2"></i>Informations familiales
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                    
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-male me-1 text-muted"></i>Nom du père
                                                    </label>
                                                    <input type="text" class="form-control @error('editUser.nom_pere') is-invalid @enderror"
                                                           wire:model.defer="editUser.nom_pere"
                                                           placeholder="Nom complet du père">
                                                    @error('editUser.nom_pere')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone du père
                                                    </label>
                                                    <input type="tel" class="form-control @error('editUser.tel_pere') is-invalid @enderror"
                                                           wire:model.defer="editUser.tel_pere"
                                                           placeholder="+261 XX XX XXX XX">
                                                    @error('editUser.tel_pere')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-female me-1 text-muted"></i>Nom de la mère
                                                    </label>
                                                    <input type="text" class="form-control @error('editUser.nom_mere') is-invalid @enderror"
                                                           wire:model.defer="editUser.nom_mere"
                                                           placeholder="Nom complet de la mère">
                                                    @error('editUser.nom_mere')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone de la mère
                                                    </label>
                                                    <input type="tel" class="form-control @error('editUser.tel_mere') is-invalid @enderror"
                                                           wire:model.defer="editUser.tel_mere"
                                                           placeholder="+261 XX XX XXX XX">
                                                    @error('editUser.tel_mere')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-user-tie me-1 text-muted"></i>Nom du tuteur
                                                    </label>
                                                    <input type="text" class="form-control @error('editUser.nom_tuteur') is-invalid @enderror"
                                                           wire:model.defer="editUser.nom_tuteur"
                                                           placeholder="Nom complet du tuteur (optionnel)">
                                                    @error('editUser.nom_tuteur')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone du tuteur
                                                    </label>
                                                    <input type="tel" class="form-control @error('editUser.tel_tuteur') is-invalid @enderror"
                                                           wire:model.defer="editUser.tel_tuteur"
                                                           placeholder="+261 XX XX XXX XX">
                                                    @error('editUser.tel_tuteur')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Payment History Section -->
                            @if(isset($payments) && count($payments) > 0)
                                <div class="col-12 mt-4">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-warning text-dark">
                                            <h5 class="mb-0">
                                                <i class="fa fa-credit-card me-2"></i>Historique des paiements
                                                <span class="badge bg-dark ms-2">{{ count($payments) }}</span>
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-hover table-sm">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th><i class="fa fa-calendar me-1"></i>Date</th>
                                                            <th><i class="fa fa-money-bill me-1"></i>Montant</th>
                                                            <th><i class="fa fa-tag me-1"></i>Type</th>
                                                            <th><i class="fa fa-check-circle me-1"></i>État</th>
                                                            <th class="text-center"><i class="fa fa-cogs me-1"></i>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($payments as $payment)
                                                            <tr>
                                                                <td>
                                                                    <small class="text-muted">
                                                                        {{ $payment->created_at->format('d/m/Y H:i') }}
                                                                    </small>
                                                                </td>
                                                                <td>
                                                                    <strong class="text-success">{{ number_format($payment->montant, 0, ',', ' ') }} Ar</strong>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-info">{{ $payment->payment->nom ?? 'N/A' }}</span>
                                                                </td>
                                                                <td>
                                                                    @if($payment->is_valid_sec)
                                                                        <span class="badge bg-success">
                                                                            <i class="fa fa-check me-1"></i>Validé
                                                                        </span>
                                                                    @else
                                                                        <span class="badge bg-warning">
                                                                            <i class="fa fa-clock me-1"></i>En attente
                                                                        </span>
                                                                    @endif
                                                                </td>
                                                                <td class="text-center">
                                                                    @if(!$payment->is_valid_sec)
                                                                        <button type="button" class="btn btn-sm btn-success btn-action"
                                                                                wire:click="validerPaiement({{ $payment->id }})"
                                                                                title="Valider ce paiement">
                                                                            <i class="fa fa-check"></i>
                                                                        </button>
                                                                    @else
                                                                        <span class="text-muted">
                                                                            <i class="fa fa-check-circle"></i>
                                                                        </span>
                                                                    @endif
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </form>

                        <!-- Modal Footer with Enhanced Actions -->
                        <div class="modal-footer bg-light">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div class="text-muted">
                                    <small>
                                        <i class="fa fa-info-circle me-1"></i>
                                        Les champs marqués d'un <span class="text-danger">*</span> sont obligatoires
                                    </small>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2"
                                            wire:click="closeAllModals">
                                        <i class="fa fa-times me-1"></i>Annuler
                                    </button>
                                    <button type="submit" class="btn btn-primary btn-action"
                                            wire:click="updateUser"
                                            wire:loading.attr="disabled">
                                        <span wire:loading.remove wire:target="updateUser">
                                            <i class="fa fa-save me-1"></i>Enregistrer
                                        </span>
                                        <span wire:loading wire:target="updateUser">
                                            <i class="fa fa-spinner fa-spin me-1"></i>Enregistrement...
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-3 text-muted">Chargement des données de l'étudiant...</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Payment Modal -->
    @if($showPaymentModal)
    <div class="modal fade show d-block" tabindex="-1" role="dialog" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title d-flex align-items-center">
                        <i class="fa fa-credit-card me-2"></i>
                        Historique des paiements - <strong class="ms-1">{{ $selectedUserName }}</strong>
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeAllModals" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    @if(count($payments) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-primary">
                                    <tr>
                                        <th><i class="fa fa-calendar me-1"></i>Date</th>
                                        <th><i class="fa fa-money-bill me-1"></i>Montant</th>
                                        <th><i class="fa fa-tag me-1"></i>Type</th>
                                        <th><i class="fa fa-check-circle me-1"></i>État</th>
                                        <th class="text-center"><i class="fa fa-cogs me-1"></i>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($payments as $payment)
                                        <tr>
                                            <td>
                                                <div>{{ $payment->created_at->format('d/m/Y') }}</div>
                                                <small class="text-muted">{{ $payment->created_at->format('H:i') }}</small>
                                            </td>
                                            <td>
                                                <strong class="text-success fs-5">{{ number_format($payment->montant, 0, ',', ' ') }} Ar</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $payment->payment->nom ?? 'N/A' }}</span>
                                            </td>
                                            <td>
                                                @if($payment->is_valid_sec)
                                                    <span class="badge bg-success">
                                                        <i class="fa fa-check me-1"></i>Validé
                                                    </span>
                                                @else
                                                    <span class="badge bg-warning">
                                                        <i class="fa fa-clock me-1"></i>En attente
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if(!$payment->is_valid_sec)
                                                    <button type="button" class="btn btn-sm btn-success btn-action"
                                                            wire:click="validerPaiement({{ $payment->id }})"
                                                            title="Valider ce paiement">
                                                        <i class="fa fa-check me-1"></i>Valider
                                                    </button>
                                                @else
                                                    <span class="text-success">
                                                        <i class="fa fa-check-circle fa-lg"></i>
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Payment Summary -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="fs-4 fw-bold text-primary">{{ count($payments) }}</div>
                                    <small class="text-muted">Total paiements</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="fs-4 fw-bold text-success">{{ $payments->where('is_valid_sec', 1)->count() }}</div>
                                    <small class="text-muted">Validés</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="fs-4 fw-bold text-warning">{{ $payments->where('is_valid_sec', 0)->count() }}</div>
                                    <small class="text-muted">En attente</small>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fa fa-credit-card fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun paiement enregistré</h5>
                            <p class="text-muted">Cet étudiant n'a effectué aucun paiement pour le moment.</p>
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeAllModals">
                        <i class="fa fa-times me-1"></i>Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- New Student Modal -->
    <div wire:ignore.self class="modal fade" id="modal-new-student" tabindex="-1" role="dialog" aria-labelledby="modal-new-student" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un nouvel étudiant</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Form will be added here for new student creation -->
                    <form wire:submit.prevent="createUser">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Nom <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Prénom</label>
                                <input type="text" class="form-control">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Téléphone <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Niveau <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">Sélectionner</option>
                                    @foreach ($niveaux as $niveau)
                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Année Universitaire <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">Sélectionner</option>
                                    @foreach ($annees as $annee)
                                        <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" class="btn btn-alt-secondary me-2" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-fw fa-plus me-1"></i> Créer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Enhanced notification system
        window.addEventListener("showSuccessMessage", event => {
            showNotification('success', event.detail.message || 'Opération effectuée avec succès!');
        });

        window.addEventListener("showErrorMessage", event => {
            showNotification('error', event.detail.message || 'Une erreur est survenue!');
        });

        // Modern notification function
        function showNotification(type, message) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fa fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Enhanced modal management
        window.addEventListener('closeModal', event => {
            // Close any open modals
            document.querySelectorAll('.modal.show').forEach(modal => {
                modal.classList.remove('show', 'd-block');
                modal.style.display = 'none';
            });
            document.body.classList.remove('modal-open');
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Escape key to close modals
            if (e.key === 'Escape') {
                @this.call('closeAllModals');
            }

            // Ctrl+F to focus search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                const searchInput = document.querySelector('input[wire\\:model*="query"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });

        // Auto-save user preferences
        window.addEventListener('compactViewToggled', event => {
            localStorage.setItem('infoetu_compact_view', event.detail.compact);
        });

        window.addEventListener('filtersToggled', event => {
            localStorage.setItem('infoetu_show_filters', event.detail.show);
        });

        // Restore user preferences
        const compactView = localStorage.getItem('infoetu_compact_view') === 'true';
        const showFilters = localStorage.getItem('infoetu_show_filters') !== 'false';

        if (compactView !== @this.compactView) {
            @this.set('compactView', compactView);
        }
        if (showFilters !== @this.showFilters) {
            @this.set('showFilters', showFilters);
        }

        // Enhanced table interactions
        document.addEventListener('click', function(e) {
            // Handle sortable headers
            if (e.target.closest('th[wire\\:click*="sortBy"]')) {
                const th = e.target.closest('th');
                th.style.opacity = '0.7';
                setTimeout(() => th.style.opacity = '1', 200);
            }
        });

        // Smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });

    // Refresh statistics function
    function refreshStats() {
        @this.call('loadStatistics');
        showNotification('success', 'Statistiques actualisées');
    }

    // Enhanced loading states
    document.addEventListener('livewire:load', function () {
        Livewire.hook('message.sent', (message, component) => {
            // Show loading state
            document.body.style.cursor = 'wait';
        });

        Livewire.hook('message.processed', (message, component) => {
            // Hide loading state
            document.body.style.cursor = 'default';
        });
    });
</script>