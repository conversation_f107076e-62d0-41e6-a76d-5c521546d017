<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoriquePayment;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class InfoEtu extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";

    // Remplacer le système de pages par un état modal moderne
    public $showEditModal = false;
    public $showPaymentModal = false;
    public $showDeleteModal = false;

    // Propriétés pour la gestion des données
    public $editUser = [];
    public $selectedUserId;
    public $selectedUserName = '';
    public $query = '';
    public $payments = [];
    public $filtreNiveau = '';
    public $filtreAnnee = '';
    public $filtreRempli = '';
    public $perPage = 25;

    // Nouvelles propriétés pour améliorer l'UX
    public $isLoading = false;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $showFilters = true;
    public $compactView = false;

    // Statistiques pour le dashboard
    public $totalEtudiants = 0;
    public $totalRemplis = 0;
    public $totalNonRemplis = 0;

    protected $listeners = [
        "selectDate" => 'getSelectedDate',
        "refreshInfoEtu" => '$refresh',
        "closeModal" => 'closeAllModals'
    ];

    public function mount()
    {
        $this->loadStatistics();
    }

    public function updatingQuery()
    {
        $this->resetPage();
        $this->isLoading = true;
    }

    public function updatingFiltreAnnee()
    {
        $this->resetPage();
        $this->loadStatistics();
    }

    public function updatingFiltreNiveau()
    {
        $this->resetPage();
        $this->loadStatistics();
    }

    public function updatingFiltreRempli()
    {
        $this->resetPage();
        $this->loadStatistics();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    // Méthode pour charger les statistiques
    public function loadStatistics()
    {
        $baseQuery = InscriptionStudent::whereRelation('user.roles', 'role_id', '=', 5);

        if ($this->filtreAnnee) {
            $baseQuery->where('annee_universitaire_id', $this->filtreAnnee);
        }

        if ($this->filtreNiveau) {
            $baseQuery->where('niveau_id', $this->filtreNiveau);
        }

        $this->totalEtudiants = $baseQuery->count();
        $this->totalRemplis = $baseQuery->whereNotNull('parcour_id')->count();
        $this->totalNonRemplis = $baseQuery->whereNull('parcour_id')->count();
    }


    public function render()
    {
        Carbon::setLocale("fr");

        // Optimisation: Utiliser des requêtes plus efficaces avec eager loading
        $persQuery = InscriptionStudent::query()
            ->with([
                'user:id,nom,prenom,telephone1,sexe,photo,email,matricule,date_naissance,lieu_naissance,adresse,cin',
                'parcours:id,sigle,nom',
                'niveau:id,nom',
                'annee:id,nom'
            ]);

        // Optimisation: Recherche plus efficace
        if (!empty($this->query)) {
            $searchTerm = '%' . $this->query . '%';
            $persQuery->whereHas('user', function ($query) use ($searchTerm) {
                $query->where(function($q) use ($searchTerm) {
                    $q->where('nom', 'like', $searchTerm)
                      ->orWhere('prenom', 'like', $searchTerm)
                      ->orWhere('matricule', 'like', $searchTerm)
                      ->orWhereRaw("CONCAT(nom, ' ', prenom) LIKE ?", [$searchTerm]);
                });
            });
        }

        // Filtres améliorés
        if (!empty($this->filtreNiveau)) {
            $persQuery->where('niveau_id', $this->filtreNiveau);
        }

        if (!empty($this->filtreAnnee)) {
            $persQuery->where('annee_universitaire_id', $this->filtreAnnee);
        }

        if (!empty($this->filtreRempli)) {
            if ($this->filtreRempli == "non_rempli") {
                $persQuery->whereNull('parcour_id');
            } elseif ($this->filtreRempli == "rempli") {
                $persQuery->whereNotNull('parcour_id');
            }
        }

        // Tri
        $persQuery->orderBy($this->sortField, $this->sortDirection);

        // Statistiques par parcours optimisées
        $etuStatus = Parcour::withCount([
            'info as etu_count' => function ($query) {
                $query->whereRelation('user.roles', 'role_id', '=', 5);
                if (!empty($this->filtreAnnee)) {
                    $query->where('annee_universitaire_id', $this->filtreAnnee);
                }
                if (!empty($this->filtreNiveau)) {
                    $query->where('niveau_id', $this->filtreNiveau);
                }
            }
        ])->orderBy('nom')->get();

        return view('livewire.secretaire.infoetu.index', [
            "etus" => $persQuery->whereRelation('user.roles', 'role_id', '=', 5)
                ->paginate($this->perPage),
            "etuStatus" => $etuStatus,
            "niveaux" => $this->getCachedNiveaux(),
            "annees" => $this->getCachedAnnees(),
            "parcours" => $this->getCachedParcours(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    // Méthodes de cache pour éviter les requêtes répétées
    protected function getCachedParcours()
    {
        return cache()->remember('parcours_list', 3600, function() {
            return Parcour::select(['id', 'sigle', 'nom'])->orderBy('sigle')->get();
        });
    }

    protected function getCachedAnnees()
    {
        return cache()->remember('annees_list', 3600, function() {
            return AnneeUniversitaire::select(['id', 'nom'])->orderBy('nom', 'desc')->get();
        });
    }

    protected function getCachedNiveaux()
    {
        return cache()->remember('niveaux_list', 3600, function() {
            return Niveau::select(['id', 'nom'])->orderBy('nom')->get();
        });
    }

    // Méthodes pour les modals
    public function openEditModal($userId)
    {
        $this->selectedUserId = $userId;
        $user = User::find($userId);
        $this->editUser = $user->toArray();
        $this->selectedUserName = $user->nom . ' ' . $user->prenom;
        $this->populatePay();
        $this->showEditModal = true;
        $this->dispatchBrowserEvent("helperDatePicker");
    }

    public function openPaymentModal($userId)
    {
        $this->selectedUserId = $userId;
        $user = User::find($userId);
        $this->selectedUserName = $user->nom . ' ' . $user->prenom;
        $this->populatePay();
        $this->showPaymentModal = true;
    }

    public function closeAllModals()
    {
        $this->showEditModal = false;
        $this->showPaymentModal = false;
        $this->showDeleteModal = false;
        $this->editUser = [];
        $this->selectedUserId = null;
        $this->selectedUserName = '';
        $this->payments = [];
    }

    public function getSelectedDate($date, $type = 'date_naissance')
    {
        if ($type === 'date_naissance') {
            $this->editUser["date_naissance"] = $date;
        } elseif ($type === 'date_delivrance') {
            $this->editUser["date_delivrance"] = $date;
        }
    }

    // Règles de validation consolidées
    protected function rules()
    {
        return [
            'editUser.nom' => 'required|string|max:255',
            'editUser.prenom' => 'nullable|string|max:255',
            'editUser.email' => 'nullable|email|max:255',
            'editUser.sexe' => 'required|in:H,F',
            'editUser.date_naissance' => 'required|date|before:today',
            'editUser.lieu_naissance' => 'required|string|max:255',
            'editUser.adresse' => 'nullable|string|max:500',
            'editUser.telephone2' => 'nullable|string|max:20',
            'editUser.cin' => 'nullable|string|max:20',
            'editUser.nom_pere' => 'nullable|string|max:255',
            'editUser.nom_mere' => 'nullable|string|max:255',
            'editUser.tel_pere' => 'nullable|string|max:20',
            'editUser.tel_mere' => 'nullable|string|max:20',
            'editUser.nom_tuteur' => 'nullable|string|max:255',
            'editUser.tel_tuteur' => 'nullable|string|max:20',
            'editUser.date_delivrance' => 'nullable|date',
            'editUser.lieu_delivrance' => 'nullable|string|max:255',
            'editUser.duplicata' => 'nullable|string|max:255',
            'editUser.parcour_id' => 'required|exists:parcours,id',
            'editUser.niveau_id' => 'required|exists:niveaux,id',
            'editUser.telephone1' => ['required', 'string', 'max:20', Rule::unique("users", "telephone1")->ignore($this->editUser['id'] ?? null)],
        ];
    }

    // Méthodes de compatibilité (à supprimer progressivement)
    public function goToListUser()
    {
        $this->closeAllModals();
    }

    public function populatePay()
    {
        if (isset($this->editUser["id"])) {
            $this->payments = HistoriquePayment::with(['user', 'payment'])
                ->where('user_id', $this->editUser["id"])
                ->where(function ($query) {
                    $query->where('type_payment_id', 1)
                        ->orWhere('type_payment_id', 2);
                })
                ->orderBy('created_at', 'desc')
                ->get();
        }
    }

    // Méthodes de compatibilité (remplacées par openEditModal)
    public function goToCreateUser($id)
    {
        $this->openEditModal($id);
    }

    public function goToEditUser($id)
    {
        $this->openEditModal($id);
    }

    public function valider(HistoriquePayment $historique)
    {
        $historique->update([
            "is_valid_sec" => 1,
        ]);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Validé avec succès!"]);
        $this->populatePay();
    }

    public function updateUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        $validationAttributes["editUser"]["photo"] = "media/avatars/avatar0.jpg";

        // Récupérer l'utilisateur existant
        $user = User::find($this->editUser["id"]);

        // Récupérer l'année universitaire actuelle de l'étudiant
        $currentInscription = InscriptionStudent::where('user_id', $this->editUser["id"])
            ->latest()
            ->first();
        $anneeUniversitaireId = $currentInscription->annee_universitaire_id ?? null;
        
        // Vérifier si l'utilisateur n'a pas déjà un matricule
        if (empty($user->matricule)) {
            // Récupérer l'année universitaire
            $anneeUniv = AnneeUniversitaire::find($anneeUniversitaireId);

            if ($anneeUniv) {
                // Compter les étudiants qui ont déjà un matricule généré pour cette année
                // en extrayant l'année du matricule existant pour s'assurer qu'on compte bien pour la bonne année
                $anneeFormatted = '';
                if (strpos($anneeUniv->nom, '/') !== false) {
                    $anneeParts = explode('/', $anneeUniv->nom);
                    $anneeFormatted = substr(end($anneeParts), -2);
                } else {
                    $anneeFormatted = substr($anneeUniv->nom, -2);
                }

                // Compter les matricules existants pour cette année (format: XXX/YY/IMSAA)
                $count = User::whereNotNull('matricule')
                    ->where('matricule', 'like', '%/' . $anneeFormatted . '/IMSAA')
                    ->count();

                // Incrémenter le compteur et formater avec des zéros à gauche (001, 002, etc.)
                $countFormatted = str_pad($count + 1, 3, '0', STR_PAD_LEFT);

                // Générer le matricule au nouveau format 001/25/IMSAA
                $matricule = $countFormatted . '/' . $anneeFormatted . '/IMSAA';

                // Mettre à jour le matricule
                $validationAttributes["editUser"]["matricule"] = $matricule;
            }
        }

        // Mettre à jour l'utilisateur existant
        $user->update($validationAttributes["editUser"]);
        $user->update([
            "is_filled" => 1,
            "inscription_date" => now()->format('d-m-Y'),
        ]);

        $user->info()->update([
            "niveau_id" => $this->editUser['niveau_id'],
            "parcour_id" => $this->editUser['parcour_id'],
        ]);

        $this->closeAllModals();
        $this->loadStatistics();
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Étudiant mis à jour avec succès!"]);
    }

    public function createUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // Définir une photo par défaut
        $validationAttributes["editUser"]["photo"] = "media/avatars/avatar0.jpg";
        
        // Déterminer l'année universitaire actuelle
        $anneeUniversitaireId = AnneeUniversitaire::latest()->first()->id ?? null;
        
        // Générer le matricule
        $matricule = '';
        if ($anneeUniversitaireId) {
            // Récupérer l'année universitaire
            $anneeUniv = AnneeUniversitaire::find($anneeUniversitaireId);

            if ($anneeUniv) {
                // Compter les étudiants qui ont déjà un matricule généré pour cette année
                // en extrayant l'année du matricule existant pour s'assurer qu'on compte bien pour la bonne année
                $anneeFormatted = '';
                if (strpos($anneeUniv->nom, '/') !== false) {
                    $anneeParts = explode('/', $anneeUniv->nom);
                    $anneeFormatted = substr(end($anneeParts), -2);
                } else {
                    $anneeFormatted = substr($anneeUniv->nom, -2);
                }

                // Compter les matricules existants pour cette année (format: XXX/YY/IMSAA)
                $count = User::whereNotNull('matricule')
                    ->where('matricule', 'like', '%/' . $anneeFormatted . '/IMSAA')
                    ->count();

                // Incrémenter le compteur et formater avec des zéros à gauche (001, 002, etc.)
                $countFormatted = str_pad($count + 1, 3, '0', STR_PAD_LEFT);

                // Générer le matricule au nouveau format 001/25/IMSAA
                $matricule = $countFormatted . '/' . $anneeFormatted . '/IMSAA';
            }
        }

        // Créer le nouvel utilisateur
        $user = User::create([
            'nom' => $this->editUser['nom'] ?? '',
            'prenom' => $this->editUser['prenom'] ?? '',
            'email' => $this->editUser['email'] ?? '',
            'sexe' => $this->editUser['sexe'] ?? '',
            'matricule' => $matricule,
            'telephone1' => $this->editUser['telephone1'] ?? '',
            'date_naissance' => $this->editUser['date_naissance'] ?? null,
            'lieu_naissance' => $this->editUser['lieu_naissance'] ?? '',
            'photo' => $validationAttributes["editUser"]["photo"],
            'is_filled' => 1,
            'inscription_date' => now()->format('d-m-Y'),
            'password' => bcrypt('password'), // Définir un mot de passe par défaut
        ]);
        
        // Associer le rôle d'étudiant (ID 5)
        $user->roles()->attach(5);
        
        // Créer l'inscription de l'étudiant
        $user->info()->create([
            'niveau_id' => $this->editUser['niveau_id'] ?? null,
            'parcour_id' => $this->editUser['parcour_id'] ?? null,
            'annee_universitaire_id' => $anneeUniversitaireId,
        ]);

        $this->closeAllModals();
        $this->loadStatistics();
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Nouvel étudiant ajouté avec succès!"]);
    }

    // Nouvelles méthodes pour améliorer l'UX
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
        $this->dispatchBrowserEvent('filtersToggled', ['show' => $this->showFilters]);
    }

    public function toggleCompactView()
    {
        $this->compactView = !$this->compactView;
        $this->dispatchBrowserEvent('compactViewToggled', ['compact' => $this->compactView]);
    }

    public function clearFilters()
    {
        $this->query = '';
        $this->filtreNiveau = '';
        $this->filtreAnnee = '';
        $this->filtreRempli = '';
        $this->resetPage();
        $this->loadStatistics();
    }



    // Méthode pour valider un paiement
    public function validerPaiement($paymentId)
    {
        try {
            $payment = HistoriquePayment::find($paymentId);
            if ($payment) {
                $payment->update(['is_valid_sec' => 1]);
                $this->populatePay();
                $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Paiement validé avec succès!"]);
            } else {
                $this->dispatchBrowserEvent("showErrorMessage", ["message" => "Paiement non trouvé"]);
            }
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent("showErrorMessage", ["message" => "Erreur lors de la validation du paiement"]);
        }
    }

    // Validation en temps réel
    public function updated($propertyName)
    {
        // Validation en temps réel pour certains champs critiques
        if (str_starts_with($propertyName, 'editUser.')) {
            $field = str_replace('editUser.', '', $propertyName);

            // Validation spécifique par champ
            switch ($field) {
                case 'telephone1':
                    $this->validateOnly('editUser.telephone1');
                    break;
                case 'email':
                    if (!empty($this->editUser['email'])) {
                        $this->validateOnly('editUser.email');
                    }
                    break;
                case 'nom':
                case 'sexe':
                case 'date_naissance':
                case 'lieu_naissance':
                case 'parcour_id':
                case 'niveau_id':
                    $this->validateOnly('editUser.' . $field);
                    break;
            }
        }
    }

    // Messages de validation personnalisés
    protected function messages()
    {
        return [
            'editUser.nom.required' => 'Le nom est obligatoire.',
            'editUser.nom.string' => 'Le nom doit être une chaîne de caractères.',
            'editUser.nom.max' => 'Le nom ne peut pas dépasser 255 caractères.',

            'editUser.prenom.string' => 'Le prénom doit être une chaîne de caractères.',
            'editUser.prenom.max' => 'Le prénom ne peut pas dépasser 255 caractères.',

            'editUser.email.email' => 'L\'adresse email doit être valide.',
            'editUser.email.max' => 'L\'email ne peut pas dépasser 255 caractères.',

            'editUser.sexe.required' => 'Le sexe est obligatoire.',
            'editUser.sexe.in' => 'Le sexe doit être Homme (H) ou Femme (F).',

            'editUser.date_naissance.required' => 'La date de naissance est obligatoire.',
            'editUser.date_naissance.date' => 'La date de naissance doit être une date valide.',
            'editUser.date_naissance.before' => 'L\'étudiant doit être majeur.',

            'editUser.lieu_naissance.required' => 'Le lieu de naissance est obligatoire.',
            'editUser.lieu_naissance.string' => 'Le lieu de naissance doit être une chaîne de caractères.',
            'editUser.lieu_naissance.max' => 'Le lieu de naissance ne peut pas dépasser 255 caractères.',

            'editUser.telephone1.required' => 'Le numéro de téléphone principal est obligatoire.',
            'editUser.telephone1.string' => 'Le numéro de téléphone doit être une chaîne de caractères.',
            'editUser.telephone1.max' => 'Le numéro de téléphone ne peut pas dépasser 20 caractères.',
            'editUser.telephone1.unique' => 'Ce numéro de téléphone est déjà utilisé par un autre étudiant.',

            'editUser.parcour_id.required' => 'Le parcours est obligatoire.',
            'editUser.parcour_id.exists' => 'Le parcours sélectionné n\'existe pas.',

            'editUser.niveau_id.required' => 'Le niveau est obligatoire.',
            'editUser.niveau_id.exists' => 'Le niveau sélectionné n\'existe pas.',

            'editUser.adresse.string' => 'L\'adresse doit être une chaîne de caractères.',
            'editUser.adresse.max' => 'L\'adresse ne peut pas dépasser 500 caractères.',

            'editUser.cin.string' => 'Le numéro CIN doit être une chaîne de caractères.',
            'editUser.cin.max' => 'Le numéro CIN ne peut pas dépasser 20 caractères.',

            'editUser.date_delivrance.date' => 'La date de délivrance doit être une date valide.',
        ];
    }

    // Attributs personnalisés pour les messages d'erreur
    protected function validationAttributes()
    {
        return [
            'editUser.nom' => 'nom',
            'editUser.prenom' => 'prénom',
            'editUser.email' => 'email',
            'editUser.sexe' => 'sexe',
            'editUser.date_naissance' => 'date de naissance',
            'editUser.lieu_naissance' => 'lieu de naissance',
            'editUser.telephone1' => 'téléphone principal',
            'editUser.telephone2' => 'téléphone secondaire',
            'editUser.adresse' => 'adresse',
            'editUser.cin' => 'CIN',
            'editUser.parcour_id' => 'parcours',
            'editUser.niveau_id' => 'niveau',
        ];
    }
}